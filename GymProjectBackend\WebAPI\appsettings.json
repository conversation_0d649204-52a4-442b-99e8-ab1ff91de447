{"Environment": "dev", "ConnectionStrings": {"dev": "Server=localhost;Database=GymProject;Trusted_Connection=true;Encrypt=False;Max Pool Size=200;Min Pool Size=10;Connection Lifetime=300;Command Timeout=60;Pooling=true;Connection Timeout=30;", "staging": "Server=localhost;User Id=sa;Password=************;Database=Staging;Trusted_Connection=false;Encrypt=False;Max Pool Size=400;Min Pool Size=20;Connection Lifetime=300;Command Timeout=60;Pooling=true;Connection Timeout=30;", "canlı": "Server=localhost;User Id=sa;Password=************;Database=GymProject;Trusted_Connection=false;Encrypt=False;Max Pool Size=600;Min Pool Size=30;Connection Lifetime=300;Command Timeout=60;Pooling=true;Connection Timeout=30;"}, "AllowedOrigins": {"dev": ["*"], "staging": ["https://staging.gymkod.com"], "canlı": ["https://admin.gymkod.com"]}, "TokenOptions": {"dev": {"Audience": "http://localhost:4200", "Issuer": "http://localhost:5165", "AccessTokenExpiration": 2, "RefreshTokenExpiration": 30, "SecurityKey": "DevEnv_2024_Gym_zX9c2Kf8Lm3Qp7Rt5Vw1Hy4Njt6Bd0Gs3Ae8Iu2Zy5Tx7Fq1Cm9Pk4Wv6Hn2Jl8Rd0Se3Gb7Mt_LocalDev"}, "staging": {"Audience": "https://stagingapi.gymkod.com", "Issuer": "https://staging.gymkod.com", "AccessTokenExpiration": 15, "RefreshTokenExpiration": 30, "SecurityKey": "StagingEnv_2024_Gym_Kp8Nm5Qr2Wt9Yx6Cv3Bn7Ml4Jh1Fg0Ds8Ae5Iu7Zy2Tx4Fq6Cm1Pk9Wv3Hn5Jl7Rd2Se0Gb4Mt8Lk"}, "canlı": {"Audience": "https://api.gymkod.com", "Issuer": "https://admin.gymkod.com", "AccessTokenExpiration": 15, "RefreshTokenExpiration": 30, "SecurityKey": "ProdEnv_2024_Gym_Secure_Rt9Yx2Cv8Bn4Ml7Jh3Fg6Ds1Ae0Iu5Zy8Tx1Fq4Cm7Pk2Wv0Hn6Jl9Rd5Se3Gb1Mt4Lk7Qp"}}, "IpRateLimiting": {"EnableEndpointRateLimiting": true, "StackBlockedRequests": false, "RealIpHeader": "X-Real-IP", "ClientIdHeader": "X-ClientId", "HttpStatusCode": 429, "IpWhitelist": [], "EndpointWhitelist": ["get:/api/health", "*:/swagger/*", "post:/api/user/upload-profile-image"], "GeneralRules": [{"Endpoint": "*", "Period": "1m", "Limit": 100}, {"Endpoint": "*", "Period": "1h", "Limit": 1000}], "SpecificRules": [{"Endpoint": "GET:/api/member/getall*", "Period": "1m", "Limit": 30}, {"Endpoint": "GET:/api/user/getall", "Period": "1m", "Limit": 30}, {"Endpoint": "GET:/api/user/profile", "Period": "1m", "Limit": 15}, {"Endpoint": "POST:/api/member/scannumber", "Period": "10s", "Limit": 1}, {"Endpoint": "GET:/api/member/getbyphone", "Period": "10s", "Limit": 1}, {"Endpoint": "POST:/api/auth/change-password", "Period": "5m", "Limit": 5}, {"Endpoint": "GET:/api/user/remaining-profile-image-uploads", "Period": "1m", "Limit": 10}, {"Endpoint": "GET:/api/member/getmemberqrbyuserid", "Period": "3m", "Limit": 1}, {"Endpoint": "GET:/api/member/gettodayentries", "Period": "30s", "Limit": 2}, {"Endpoint": "GET:/api/member/gettotalregisteredmembers", "Period": "1m", "Limit": 10}, {"Endpoint": "GET:/api/member/gettotalactivemembers", "Period": "1m", "Limit": 10}, {"Endpoint": "GET:/api/payment/getpaymenthistorypaginated", "Period": "30s", "Limit": 5}, {"Endpoint": "GET:/api/payment/getpaymenttotals", "Period": "30s", "Limit": 5}, {"Endpoint": "GET:/api/products/getall", "Period": "1m", "Limit": 20}, {"Endpoint": "GET:/api/transactions/getall*", "Period": "30s", "Limit": 5}, {"Endpoint": "POST:/api/transactions/add", "Period": "5s", "Limit": 1}, {"Endpoint": "POST:/api/transactions/addbulk", "Period": "10s", "Limit": 1}, {"Endpoint": "POST:/api/products/add", "Period": "30s", "Limit": 3}, {"Endpoint": "POST:/api/products/update", "Period": "30s", "Limit": 5}, {"Endpoint": "POST:/api/products/delete", "Period": "30s", "Limit": 3}, {"Endpoint": "POST:/api/member/add", "Period": "30s", "Limit": 2}, {"Endpoint": "POST:/api/member/update", "Period": "10s", "Limit": 3}, {"Endpoint": "POST:/api/member/update-profile", "Period": "2m", "Limit": 2}, {"Endpoint": "DELETE:/api/member/delete", "Period": "1m", "Limit": 2}, {"Endpoint": "GET:/api/member/getupcomingbirthdays", "Period": "1m", "Limit": 5}, {"Endpoint": "GET:/api/member/getmemberdetails", "Period": "30s", "Limit": 10}, {"Endpoint": "POST:/api/payment/updatestatus*", "Period": "5s", "Limit": 2}, {"Endpoint": "GET:/api/expenses/getall*", "Period": "30s", "Limit": 10}, {"Endpoint": "POST:/api/expenses/add", "Period": "30s", "Limit": 3}, {"Endpoint": "GET:/api/user/remaining-file-downloads", "Period": "1m", "Limit": 10}, {"Endpoint": "POST:/api/user/record-file-download", "Period": "10m", "Limit": 5}]}, "FileSettings": {"ImagesPath": "C:\\GymProject\\Images", "AllowedExtensions": [".jpg", ".jpeg", ".png"], "MaxFileSize": 5242880}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}